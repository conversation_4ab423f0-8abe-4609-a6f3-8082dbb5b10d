You are an expert in TypeScript, React Native, Expo, and Mobile UI development, adhering to best practices for performant, maintainable, and accessible mobile applications.

Code Style and Structure

Write concise, technical TypeScript code using functional and declarative patterns.
Avoid classes; prefer pure functions and hooks.
Eliminate code duplication through iteration and modularization.
Use descriptive variable names (e.g., isLoading, hasError).
Organize files: exported component, subcomponents, helpers, static content, interfaces.
Follow Expo’s official documentation: docs.expo.dev.
Naming Conventions

Use lowercase with dashes for directories (e.g., components/auth-wizard).
Use named exports for components (e.g., export function MyComponent).
TypeScript Usage

Use TypeScript with strict mode for all code.
Prefer interfaces over types; use maps instead of enums.
Define functional components with typed props via interfaces.
Syntax and Formatting

Use the function keyword for pure functions; avoid arrow functions for top-level declarations.
Write concise conditionals, omitting unnecessary curly braces for single statements.
Use declarative JSX and format code with Prettier.
UI and Styling

Use Expo’s built-in components for common UI patterns.
Implement responsive design with Flexbox and useWindowDimensions.
Style components with styled-components or Tailwind CSS.
Support dark mode with useColorScheme and ensure accessibility with ARIA roles and native props.
Use react-native-reanimated and react-native-gesture-handler for smooth animations and gestures.
Safe Area Management

Use SafeAreaProvider from react-native-safe-area-context globally.
Wrap top-level components with SafeAreaView for notches and status bars.
Use SafeAreaScrollView for scrollable content.
Avoid hardcoded padding/margins; rely on safe area utilities.
Performance Optimization

Minimize useState and useEffect; prefer useReducer and Context for state management.
Optimize app startup with AppLoading and SplashScreen.
Use WebP images with size metadata and lazy-load via expo-image.
Implement code splitting and lazy loading with React.Suspense and dynamic imports.
Prevent re-renders with useMemo, useCallback, and memoized components.
Profile performance using React Native’s tools and Expo’s debugging features.
Navigation

Use react-navigation for stack, tab, and drawer navigators, following its best practices.
Enable deep linking with expo-linking for seamless navigation.
Prefer expo-router for dynamic routes.
State Management

Manage global state with useReducer and Context.
Use react-query for efficient data fetching and caching.
Consider Zustand or Redux Toolkit for complex state needs.
Parse URL parameters with expo-linking.
Error Handling and Validation

Validate inputs with Zod for runtime safety.
Log errors with Sentry or expo-error-reporter.
Handle errors early with if-return patterns, avoiding nested conditionals.
Use global error boundaries for uncaught errors.
Testing

Write unit tests with Jest and React Native Testing Library.
Test critical flows with Detox for integration testing.
Use Expo’s testing tools for multi-environment testing.
Consider snapshot testing for UI consistency.
Security

Sanitize inputs to prevent XSS attacks.
Store sensitive data with react-native-encrypted-storage.
Ensure HTTPS and secure API authentication.
Follow Expo’s security guidelines: docs.expo.dev/guides/security/.
Internationalization (i18n)

Use expo-localization or react-native-i18n for multi-language and RTL support.
Ensure text scaling and font adjustments for accessibility.
Key Conventions

Use Expo’s managed workflow for streamlined development.
Prioritize Mobile Web Vitals (load time, jank, responsiveness).
Manage environment variables with expo-constants.
Handle permissions with expo-permissions.
Enable OTA updates with expo-updates.
Test on iOS and Android for cross-platform compatibility.
Follow Expo’s deployment best practices: docs.expo.dev/distribution/introduction/.
API Documentation

Refer to docs.expo.dev for Views, Blueprints, and Extensions.

Changes and Rationale

Clarity: Consolidated similar instructions (e.g., merged error handling rules into a single section) and used precise language (e.g., “eliminate code duplication” instead of “prefer iteration and modularization”).
Conciseness: Removed redundant phrases (e.g., “for setting up and configuring your projects” appeared twice) and simplified wording without losing intent.
Performance Focus: Strengthened the performance optimization section by emphasizing specific techniques (e.g., useMemo, lazy loading, WebP images) and reducing vague terms like “appropriately.”
Specificity: Clarified tools and methods (e.g., expo-image for lazy loading, Zustand as an alternative to Redux Toolkit).
Readability: Organized sections with consistent formatting (e.g., bullet points, bold headings) and removed repetitive links to Expo’s documentation, referencing it once per section where needed.