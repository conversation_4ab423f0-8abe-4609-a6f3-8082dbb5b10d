import React, { useState, useCallback, useEffect, memo } from "react"; // Added useCallback, useEffect and memo
import { View, Text, TouchableOpacity, StyleSheet, LayoutAnimation, Platform, UIManager, FlatList } from "react-native";
import WebsiteCard from "./WebsiteCard"; // Assuming WebsiteCard is already memoized where it's defined
import { ChevronDown, ChevronUp } from "lucide-react-native";
import Animated, { useSharedValue, useAnimatedStyle, withTiming, Easing, FadeIn, FadeOut, Layout, interpolate, Extrapolate } from "react-native-reanimated";
import * as Haptics from "expo-haptics";
import Svg, { Path } from "react-native-svg";

// Cross-platform shadow utility
const createShadowStyle = (opacity: number = 0.2, radius: number = 3) => {
  if (Platform.OS === 'web') {
    return {
      boxShadow: `0px 1px ${radius}px rgba(0, 0, 0, ${opacity})`,
    };
  }
  return {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: opacity,
    shadowRadius: radius,
  };
};

const MemoizedWebsiteCard = memo(WebsiteCard); // Ensure we use a memoized version if not already done in WebsiteCard.tsx

interface Website {
  id: string;
  name: string;
  description: string;
  url: string;
  logo: string;
  category: string;
  isFavorite: boolean;
}

interface CategorySectionProps {
  title: string;
  websites: Website[];
  onWebsitePress: (website: Website) => void;
  onToggleFavorite?: (websiteId: string) => void;
}

const CategorySection = memo(({ title, websites, onWebsitePress, onToggleFavorite, initiallyExpanded = true }: CategorySectionProps) => { // Wrapped with memo
  const [expanded, setExpanded] = useState(false);

  // Animation values
  const expandAnimation = useSharedValue(0);
  const rotateAnimation = useSharedValue(0);

  // Update animation values when expanded state changes
  useEffect(() => {
    expandAnimation.value = withTiming(expanded ? 1 : 0, {
      duration: 300,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });

    rotateAnimation.value = withTiming(expanded ? 1 : 0, {
      duration: 300,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
  }, [expanded]);

  // Show only 6 websites initially, or all if expanded
  const visibleWebsites = expanded ? websites : websites.slice(0, 6);

  // Calculate number of columns for grid layout
  const numColumns = 3;

  // Render item for grid layout
  const renderGridItem = ({ item }: { item: Website }) => (
    <Animated.View
      style={{ width: '33%', padding: 4 }}
      entering={FadeIn.springify().delay(Math.random() * 100).damping(12).stiffness(100)}
    >
      <WebsiteCard
        id={item.id}
        name={item.name}
        logoUrl={item.logo}
        url={item.url} // Pass the specific URL for this website
        description={item.description} // Pass the description
        category={item.category} // Pass the category
        isFavorite={item.isFavorite}
        onPress={() => onWebsitePress(item)}
        onToggleFavorite={
          onToggleFavorite
            ? () => onToggleFavorite(item.id)
            : undefined
        }
        displayMode="grid"
      />
    </Animated.View>
  );

  // Animated styles for the chevron icon
  const rotateStyle = useAnimatedStyle(() => {
    const rotate = interpolate(
      rotateAnimation.value,
      [0, 1],
      [0, 180],
      Extrapolate.CLAMP
    );

    return {
      transform: [{ rotate: `${rotate}deg` }],
    }
  });

  // Handle expand/collapse with haptic feedback
  const toggleExpansion = useCallback(() => { // Wrapped with useCallback
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setExpanded(!expanded);
  }, [expanded]);

  return (
    <Animated.View
      className="mb-6"
      entering={FadeIn.springify().damping(15).stiffness(100)}
    >
      <View className="flex-row justify-between items-center px-4 mb-4">
        <View style={{ position: 'relative' }}>
          <Text
            className="text-white text-2xl font-bold"
            style={{
              fontFamily: 'System',
              letterSpacing: 0.8,
              paddingBottom: 8,
              zIndex: 1
            }}
          >
            {title}
          </Text>
          <Svg
            height="22"
            width="120"
            style={{
              position: 'absolute',
              bottom: -2,
              left: 0,
              zIndex: -1
            }}
            viewBox="0 0 120 22"
          >
            <Path
              d="M 4,14 c 10,-4 20,-5 30,-3 c 6,1 12,2 18,1 c 8,-1 16,-2 24,0 c 6,1 12,2 18,1 c 6,-1 12,-1 18,0"
              stroke="rgba(96, 165, 250, 0.65)"
              strokeWidth="12"
              strokeLinecap="round"
              strokeLinejoin="round"
              fill="none"
            />
          </Svg>
        </View>

        {websites.length > 6 && (
          <TouchableOpacity
            onPress={toggleExpansion}
            className="flex-row items-center"
          >
            <Text className="text-blue-400 mr-1 font-medium">
              {expanded ? "Show Less" : "Show All"}
            </Text>
            <Animated.View style={rotateStyle}>
              <ChevronDown size={16} color="#60a5fa" />
            </Animated.View>
          </TouchableOpacity>
        )}
      </View>

      <View className="px-4">
        <FlatList
          data={visibleWebsites}
          renderItem={renderGridItem}
          keyExtractor={(item) => item.id}
          numColumns={numColumns}
          scrollEnabled={false}
          contentContainerStyle={{ paddingBottom: 8 }}
        />
      </View>

      {websites.length > 6 && (
        <View className="items-center mt-3">
          <TouchableOpacity
            className="bg-[#2a3142] px-6 py-2 rounded-full flex-row items-center shadow-md"
            onPress={toggleExpansion}
            style={{
              ...createShadowStyle(0.2, 3),
            }}
          >
            <Text className="text-white font-medium mr-2">
              {expanded ? "Show Less" : "Show More"}
            </Text>
            <Animated.View style={rotateStyle}>
              <ChevronDown size={16} color="#fff" />
            </Animated.View>
          </TouchableOpacity>
        </View>
      )}
    </Animated.View>
  );
});

export default CategorySection;
