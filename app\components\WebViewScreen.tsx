import React, { useState, useRef, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  StyleSheet,
  Platform,
  Alert,
} from "react-native";
import { WebView } from "react-native-webview";
// Constants import was here, but not used. Removing for now. If needed, it's 'expo-constants'
import {
  ArrowLeft,
  ArrowRight,
  RefreshCw,
  X,
  Home,
  Download,
  FileDown,
} from "lucide-react-native";
// useRouter import was here, but not used. Removing for now. If needed, it's 'expo-router'
import DownloadsManager from "./DownloadsManager";
import * as FileSystem from "expo-file-system";
import * as Sharing from "expo-sharing";
import Animated, { useAnimatedStyle, useSharedValue, withTiming, Easing, FadeIn, FadeOut, SlideInDown, SlideOutUp, withSpring } from "react-native-reanimated";
import * as Haptics from "expo-haptics";
import { BlurView } from "expo-blur";
import { SafeAreaView } from 'react-native-safe-area-context';

interface WebViewScreenProps {
  url: string;
  title?: string;
  onClose?: () => void;
  onTitleChange?: (title: string) => void;
  onFaviconChange?: (favicon: string) => void;
}

const WebViewScreen = React.memo(function WebViewScreen({
  url = "https://www.imdb.com",
  title = "Website",
  onClose,
  onTitleChange,
  onFaviconChange,
}: WebViewScreenProps) {
  const [showDownloads, setShowDownloads] = useState(false);
  const webViewRef = useRef<WebView>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [canGoBack, setCanGoBack] = useState(false);
  const [canGoForward, setCanGoForward] = useState(false);
  const [currentUrl, setCurrentUrl] = useState(url);
  const [currentTitle, setCurrentTitle] = useState(title);
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);

  // Memoize the initial URL to prevent unnecessary reloads
  const initialUrl = React.useMemo(() => url, []);

  // Only update URL if it's significantly different (not just navigation within same site)
  useEffect(() => {
    if (url !== currentUrl && url !== initialUrl) {
      setCurrentUrl(url);
      setCurrentTitle(title);
    }
  }, [url, title, currentUrl, initialUrl]);

  // Debounced title change to prevent excessive calls
  const titleChangeTimeoutRef = useRef<NodeJS.Timeout>();
  useEffect(() => {
    if (currentTitle !== title && currentTitle !== "Website" && currentTitle !== "") {
      // Clear previous timeout
      if (titleChangeTimeoutRef.current) {
        clearTimeout(titleChangeTimeoutRef.current);
      }

      // Debounce the title change
      titleChangeTimeoutRef.current = setTimeout(() => {
        onTitleChange?.(currentTitle);
      }, 300);
    }

    return () => {
      if (titleChangeTimeoutRef.current) {
        clearTimeout(titleChangeTimeoutRef.current);
      }
    };
  }, [currentTitle, title, onTitleChange]);

  const handleClose = React.useCallback(() => {
    onClose?.();
  }, [onClose]);

  // Memoize the JavaScript injection to prevent recreation on every render
  const getFaviconJS = React.useMemo(() => `
    (function() {
      var link = document.querySelector("link[rel~='icon']") ||
                document.querySelector("link[rel~='shortcut icon']");
      if (link) {
        var faviconUrl = link.href;
        window.ReactNativeWebView.postMessage(JSON.stringify({type: 'favicon', url: faviconUrl}));
      }
      document.documentElement.style.webkitUserSelect = 'auto';
      document.documentElement.style.userSelect = 'auto';
      document.addEventListener('click', function(e) {
        var target = e.target;
        while(target && target.tagName !== 'A') {
          target = target.parentNode;
        }
        if (target && target.tagName === 'A' && target.href) {
          var fileExtension = target.href.split('.').pop().toLowerCase();
          var downloadExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'zip', 'rar', 'jpg', 'jpeg', 'png', 'gif', 'mp4', 'mov', 'avi', 'mkv', 'mp3', 'wav'];
          if (downloadExtensions.includes(fileExtension) || target.hasAttribute('download')) {
            e.preventDefault();
            var filename = target.getAttribute('download') || target.href.split('/').pop().split('?')[0];
            window.ReactNativeWebView.postMessage(JSON.stringify({type: 'download', url: target.href, filename: filename}));
            return false;
          }
        }
      }, true);
    })();
  `, []);

  const handleDownload = async (downloadUrl: string, filename: string) => {
    if (Platform.OS === "web") {
      window.open(downloadUrl, "_blank");
      return;
    }

    try {
      setIsDownloading(true);
      setDownloadProgress(0);

      const downloadResumable = FileSystem.createDownloadResumable(
        downloadUrl,
        FileSystem.documentDirectory + filename,
        {},
        (progressData) => {
          const progress =
            progressData.totalBytesWritten /
            progressData.totalBytesExpectedToWrite;
          setDownloadProgress(progress);
        },
      );

      const downloadResult = await downloadResumable.downloadAsync();
      setIsDownloading(false);

      if (downloadResult && downloadResult.uri) {
        Alert.alert(
          "Download Complete",
          `${filename} has been downloaded successfully.`,
          [
            { text: "OK", style: "cancel" },
            {
              text: "Share",
              onPress: async () => {
                if (await Sharing.isAvailableAsync()) {
                  await Sharing.shareAsync(downloadResult.uri);
                } else {
                  Alert.alert("Sharing not available", "Sharing is not available on this device.");
                }
              },
            },
          ],
        );
      } else {
        Alert.alert("Download Incomplete", "The download did not complete successfully or the file URI is unavailable.");
      }
    } catch (e) {
      setIsDownloading(false);
      Alert.alert("Download Failed", "Could not download the file.");
      console.error("Download error:", e);
    }
  };

  // Memoize message handler to prevent recreation
  const handleMessage = React.useCallback((event: any) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      if (data.type === "favicon" && data.url) {
        onFaviconChange?.(data.url);
      } else if (data.type === "download" && data.url) {
        handleDownload(data.url, data.filename || "download_" + Date.now());
      }
    } catch (e) {
      console.error("Error parsing WebView message:", e);
    }
  }, [onFaviconChange, handleDownload]);

  // Memoize WebView event handlers
  const handleLoadProgress = React.useCallback(({ nativeEvent }: any) => {
    setLoadingProgress(nativeEvent.progress);
  }, []);

  const handleLoadStart = React.useCallback(() => {
    setIsLoading(true);
    setLoadingProgress(0);
  }, []);

  const handleLoadEnd = React.useCallback(() => {
    setIsLoading(false);
    setLoadingProgress(1);
    // Inject JavaScript only once after load
    webViewRef.current?.injectJavaScript(getFaviconJS);
  }, [getFaviconJS]);

  const handleError = React.useCallback((syntheticEvent: any) => {
    setIsLoading(false);
    const { nativeEvent } = syntheticEvent;
    console.warn('WebView error: ', nativeEvent);
  }, []);

  const handleNavigationStateChange = React.useCallback((navState: any) => {
    setCanGoBack(navState.canGoBack);
    setCanGoForward(navState.canGoForward);

    // Only update URL if it's actually different
    if (navState.url !== currentUrl) {
      setCurrentUrl(navState.url);
    }

    // Only update title if it's actually different and not empty
    if (navState.title && navState.title !== currentTitle && navState.title.trim() !== "") {
      setCurrentTitle(navState.title);
    }
  }, [currentUrl, currentTitle]);

  // Memoize navigation handlers
  const handleNavigationPress = React.useCallback((action: () => void) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    action();
  }, []);

  const handleGoBack = React.useCallback(() => {
    handleNavigationPress(() => webViewRef.current?.goBack());
  }, [handleNavigationPress]);

  const handleGoForward = React.useCallback(() => {
    handleNavigationPress(() => webViewRef.current?.goForward());
  }, [handleNavigationPress]);

  const handleGoHome = React.useCallback(() => {
    handleNavigationPress(() => webViewRef.current?.loadUrl(initialUrl));
  }, [handleNavigationPress, initialUrl]);

  const handleRefresh = React.useCallback(() => {
    handleNavigationPress(() => webViewRef.current?.reload());
  }, [handleNavigationPress]);

  const handleShowDownloads = React.useCallback(() => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setShowDownloads(true);
  }, []);

  const handleHideDownloads = React.useCallback(() => {
    setShowDownloads(false);
  }, []);

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '#111827' }} edges={['bottom']}>
      <View className="flex-1 bg-gray-900">
        {/* Header: Title and Close Button */}
        <View className="flex-row items-center justify-between px-3 py-2 bg-gray-800 border-b border-gray-700">
          <View className="flex-1 flex-row items-center">
            {/* Favicon could go here if available */}
            <Text className="text-white font-semibold ml-2 max-w-[70%]" numberOfLines={1}>
              {currentTitle}
            </Text>
          </View>
          <TouchableOpacity onPress={handleClose} className="p-2">
            <X size={24} color="#FFF" />
          </TouchableOpacity>
        </View>

        <LoadingProgressBar progress={loadingProgress} isVisible={isLoading || loadingProgress < 1} />

        <WebView
          ref={webViewRef}
          source={{ uri: currentUrl }}
          style={styles.webview}
          onLoadProgress={handleLoadProgress}
          onLoadStart={handleLoadStart}
          onLoadEnd={handleLoadEnd}
          onError={handleError}
          onNavigationStateChange={handleNavigationStateChange}
          injectedJavaScript={getFaviconJS}
          onMessage={handleMessage}
          allowsBackForwardNavigationGestures
          pullToRefreshEnabled
          startInLoadingState={true}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          userAgent={Platform.OS === 'android' ? "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36" : undefined}
        />

        {/* Download Progress Overlay */}
        {isDownloading && (
          <Animated.View
            entering={FadeIn.duration(200)}
            exiting={FadeOut.duration(200)}
            className="absolute bottom-16 left-4 right-4 p-3 bg-gray-800 rounded-lg shadow-lg z-20"
          >
            <Text className="text-white text-sm mb-1">Downloading...</Text>
            <View className="w-full h-2 bg-gray-700 rounded-full overflow-hidden">
              <Animated.View
                style={[{ width: `${downloadProgress * 100}%`}]}
                className="h-full bg-blue-500 rounded-full"
              />
            </View>
          </Animated.View>
        )}

        {/* Bottom Navigation Bar */}
        <BlurView intensity={Platform.OS === 'ios' ? 80 : 120} tint="dark" className="border-t border-gray-700">
          <View className="flex-row justify-around items-center py-2 px-1 bg-transparent">
            <TouchableOpacity
              onPress={handleGoBack}
              disabled={!canGoBack}
              className="p-3 items-center justify-center"
            >
              <ArrowLeft size={24} color={canGoBack ? "#FFF" : "#6b7280"} />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={handleGoForward}
              disabled={!canGoForward}
              className="p-3 items-center justify-center"
            >
              <ArrowRight size={24} color={canGoForward ? "#FFF" : "#6b7280"} />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={handleGoHome}
              className="p-3 items-center justify-center"
            >
              <Home size={24} color="#FFF" />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={handleRefresh}
              className="p-3 items-center justify-center"
            >
              <RefreshCw size={24} color="#FFF" />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={handleShowDownloads}
              className="p-3 items-center justify-center"
            >
              <Download size={24} color="#FFF" />
            </TouchableOpacity>
          </View>
        </BlurView>

        {/* Downloads Manager Modal */}
        {showDownloads && (
          <Animated.View
            style={{ flex: 1, position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, zIndex: 100 }}
            entering={Platform.OS === 'ios' ? SlideInDown.duration(300) : FadeIn.duration(200)}
            exiting={Platform.OS === 'ios' ? SlideOutUp.duration(300) : FadeOut.duration(200)}
          >
            <DownloadsManager
              visible={showDownloads}
              onClose={handleHideDownloads}
            />
          </Animated.View>
        )}
      </View>
    </SafeAreaView>
  );
}); // Correctly close WebViewScreen

const styles = StyleSheet.create({
  webview: {
    backgroundColor: "#111827", // Darker background for webview container
  },
});

interface LoadingProgressBarProps {
  progress: number;
  isVisible: boolean;
}

const LoadingProgressBar: React.FC<LoadingProgressBarProps> = React.memo(({ progress, isVisible }) => {
  const width = useSharedValue(0);
  const opacity = useSharedValue(0);

  useEffect(() => {
    if (isVisible) {
      opacity.value = withTiming(1, { duration: 200 });
      width.value = withTiming(progress, {
        duration: 250, // Slightly faster
        easing: Easing.out(Easing.quad) // Smoother easing
      });
    } else {
      // Animate out then set width to 0
      opacity.value = withTiming(0, { duration: 200 }, () => {
        if (progress === 0 || progress === 1) { // Reset width after fade out if fully loaded or not started
          width.value = 0;
        }
      });
    }
  }, [progress, isVisible]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      width: `${width.value * 100}%`,
      opacity: opacity.value,
      // If opacity is 0, effectively hide by setting height to 0 as well,
      // or rely on parent to collapse if this component renders nothing.
      // For now, opacity 0 should be enough to make it visually disappear.
      // If layout space is an issue, one might add:
      // height: opacity.value === 0 ? 0 : 1,
    };
  });

  // The component will always render, but its animated style (opacity) will make it invisible
  // if opacity.value is 0. This avoids reading .value directly in the render path for conditional rendering.

  return (
    <Animated.View
      style={[
        {
          height: 2,
          backgroundColor: 'blue', // Consider using a theme color
          position: 'absolute',
          top: 0,
          left: 0,
          zIndex: 1000, // Ensure it's on top
        },
        animatedStyle,
      ]}
    />
  );
}); // Correctly close LoadingProgressBar

// Custom comparison function for memo to prevent unnecessary re-renders
}, (prevProps, nextProps) => {
  return (
    prevProps.url === nextProps.url &&
    prevProps.title === nextProps.title &&
    prevProps.onClose === nextProps.onClose &&
    prevProps.onTitleChange === nextProps.onTitleChange &&
    prevProps.onFaviconChange === nextProps.onFaviconChange
  );
});

export default WebViewScreen;
