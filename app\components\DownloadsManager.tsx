import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from "react-native";
import * as FileSystem from "expo-file-system";
import * as Sharing from "expo-sharing";
import { Trash2, Share, FileDown, X } from "lucide-react-native";

interface DownloadedFile {
  name: string;
  uri: string;
  size: number;
  modificationTime: number;
}

interface DownloadsManagerProps {
  visible: boolean;
  onClose: () => void;
}

const DownloadsManager = React.memo(({ visible, onClose }: DownloadsManagerProps) => {
  const [files, setFiles] = useState<DownloadedFile[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (visible) {
      loadDownloadedFiles();
    }
  }, [visible]);

  const loadDownloadedFiles = async () => {
    try {
      setIsLoading(true);

      if (!FileSystem.documentDirectory) {
        setFiles([]);
        setIsLoading(false);
        return;
      }

      const fileInfos = await FileSystem.readDirectoryAsync(
        FileSystem.documentDirectory,
      );

      const fileDetails = await Promise.all(
        fileInfos.map(async (fileName) => {
          const fileUri = FileSystem.documentDirectory + fileName;
          const fileInfo = await FileSystem.getInfoAsync(fileUri);

          return {
            name: fileName,
            uri: fileUri,
            size: fileInfo.size || 0,
            modificationTime: fileInfo.modificationTime || Date.now(),
          };
        }),
      );

      // Sort by most recent first
      setFiles(
        fileDetails.sort((a, b) => b.modificationTime - a.modificationTime),
      );
    } catch (error) {
      console.error("Error loading downloads:", error);
      Alert.alert("Error", "Could not load downloaded files");
    } finally {
      setIsLoading(false);
    }
  };

  const handleShare = React.useCallback(async (file: DownloadedFile) => {
    try {
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(file.uri);
      } else {
        Alert.alert(
          "Sharing not available",
          "Sharing is not available on this device",
        );
      }
    } catch (error) {
      Alert.alert("Error", "Could not share the file");
    }
  }, []);

  const handleDelete = React.useCallback(async (file: DownloadedFile) => {
    try {
      await FileSystem.deleteAsync(file.uri);
      setFiles(files.filter((f) => f.uri !== file.uri));
    } catch (error) {
      Alert.alert("Error", "Could not delete the file");
    }
  }, [files]); // Added files to dependency array as setFiles is called which depends on files state

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  if (!visible) return null;

  return (
    <View className="absolute inset-0 bg-gray-900 z-50">
      <View className="flex-row items-center justify-between px-4 py-3 bg-gray-800">
        <View className="flex-row items-center">
          <FileDown size={20} color="#fff" className="mr-2" />
          <Text className="text-white font-medium text-lg">Downloads</Text>
        </View>
        <TouchableOpacity
          onPress={onClose}
          className="p-2 rounded-full bg-gray-700"
        >
          <X size={20} color="#fff" />
        </TouchableOpacity>
      </View>

      {isLoading ? (
        <View className="flex-1 items-center justify-center">
          <Text className="text-white">Loading downloads...</Text>
        </View>
      ) : files.length === 0 ? (
        <View className="flex-1 items-center justify-center p-4">
          <Text className="text-white text-center">
            No downloaded files yet
          </Text>
          <Text className="text-gray-400 text-center mt-2">
            Files you download from websites will appear here
          </Text>
        </View>
      ) : (
        <FlatList
          data={files}
          keyExtractor={(item) => item.uri}
          className="flex-1"
          renderItem={({ item }) => (
            <View className="flex-row items-center justify-between p-4 border-b border-gray-700">
              <View className="flex-1">
                <Text className="text-white font-medium" numberOfLines={1}>
                  {item.name}
                </Text>
                <Text className="text-gray-400 text-xs mt-1">
                  {formatFileSize(item.size)} •{" "}
                  {new Date(item.modificationTime).toLocaleDateString()}
                </Text>
              </View>
              <View className="flex-row">
                <TouchableOpacity
                  onPress={() => handleShare(item)}
                  className="p-2 rounded-full bg-gray-700 mr-2"
                >
                  <Share size={18} color="#fff" />
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => handleDelete(item)}
                  className="p-2 rounded-full bg-red-700"
                >
                  <Trash2 size={18} color="#fff" />
                </TouchableOpacity>
              </View>
            </View>
          )}
        />
      )}
    </View>
  );
});

export default DownloadsManager;
