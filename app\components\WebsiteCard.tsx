import React, { useState, useEffect, memo } from "react"; // Added memo
import { View, Text, TouchableOpacity, Platform, StyleSheet } from "react-native"; // Removed core Image
import { Image } from "expo-image"; // Reintroduced expo-image
import { ExternalLink, Star, Globe, Film, Tv, BookOpen, ImageOff } from "lucide-react-native";
import Animated, { useAnimatedStyle, useSharedValue, withTiming, withSpring, Easing, withSequence, FadeIn } from "react-native-reanimated";
import * as Haptics from "expo-haptics";


interface WebsiteCardProps {
  id: string;
  name: string;
  description?: string;
  logoUrl: string;
  url?: string;
  isFavorite?: boolean;
  onPress: (site: {
    id: string;
    name: string;
    description?: string;
    logoUrl: string;
    url?: string;
    isFavorite?: boolean;
  }) => void;
  onToggleFavorite?: () => void;
  displayMode?: "horizontal" | "vertical" | "grid";
  category?: string;
}

const WebsiteCard = ({
  id = "1",
  name = "IMDb",
  description,
  logoUrl = "imdb",
  url = "https://www.imdb.com",
  isFavorite = false,
  onPress = () => {},
  onToggleFavorite,
  displayMode = "horizontal",
  category = "Movies",
}: WebsiteCardProps) => {
  // Animation values
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);
  
  // Handle the press event with the website data
  const handlePress = () => {
    // Provide haptic feedback
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    // Animate the press
    scale.value = withSequence(
      withTiming(0.95, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );
    
    onPress({ id, name, description, logoUrl, url, isFavorite });
  };
  
  // Handle favorite toggle with animation
  const handleToggleFavorite = () => {
    if (onToggleFavorite) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      onToggleFavorite();
    }
  };

  // State for image loading and errors
  const [imageLoading, setImageLoading] = useState(true); // Assume loading initially
  const [imageLoadError, setImageLoadError] = useState(false);

  // Determine the image source based on the logoUrl
  const getImageSource = () => {
    if (typeof logoUrl === 'string' && logoUrl.startsWith('http')) {
      // console.log(`[getImageSource for ${name}] Using provided logoUrl: ${logoUrl}`);
      return { uri: logoUrl };
    }
    // console.warn(`[getImageSource for ${name}] logoUrl is not a valid http(s) URL ("${logoUrl}"). Falling back to placeholder.`);
    return { uri: "https://i.imgur.com/UPFOgXv.png" }; // Default placeholder
  };
  
  // Get the appropriate icon based on category
  const getCategoryIcon = () => {
    switch(category) {
      case "Movies":
        return <Film size={16} color="#FF5252" style={{ marginRight: 4 }} />;
      case "TV Shows":
        return <Tv size={16} color="#2196F3" style={{ marginRight: 4 }} />;
      case "Books":
        return <BookOpen size={16} color="#4CAF50" style={{ marginRight: 4 }} />;
      default:
        return <Globe size={16} color="#9E9E9E" style={{ marginRight: 4 }} />;
    }
  };
  
  // Animated styles
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
      opacity: opacity.value,
    };
  });

  // Horizontal mode (compact) - iPhone style
  if (displayMode === "horizontal") {
    return (
      <Animated.View style={animatedStyle}>
        <TouchableOpacity
          className="items-center mx-3 w-16"
          onPress={handlePress}
          activeOpacity={0.7}
        >
          <View style={{ 
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.3,
            shadowRadius: 5,
            elevation: 8,
            borderRadius: 12
          }}>
            {imageLoading ? (
              <SkeletonPlaceholder width={64} height={64} style={{ borderRadius: 12 }} />
            ) : imageLoadError ? (
              <View style={[styles.errorIconContainer, { width: 64, height: 64 }]}>
                <ImageOff size={32} color="#9ca3af" /> {/* bg-gray-400 */}
              </View>
            ) : (
              <Image
                source={getImageSource()} // getImageSource now returns an object { uri: string } or a number for local assets
                style={styles.logoImageHorizontal} // Use StyleSheet for image styles
                contentFit="contain"
                placeholder={require('../../../assets/images/partial-react-logo.png')} // Added a placeholder for expo-image
                transition={300}
                onLoadStart={() => {
                  setImageLoading(true);
                  setImageLoadError(false);
                }}
                onLoadEnd={() => setImageLoading(false)}
                onError={(e) => {
                  console.error(`[Expo Image Error - Horizontal for ${name}] Source: ${JSON.stringify(getImageSource())}, Error: ${e.error?.message || 'Unknown error'}`);
                  setImageLoading(false);
                  setImageLoadError(true);
                }}
              />
            )}
          </View>
          <View className="flex-row items-center mt-2 justify-center">
            <View className="mr-1">
              {getCategoryIcon()}
            </View>
            <Text
              className="text-white text-xs text-center font-bold"
              numberOfLines={1}
              style={{ fontFamily: 'System', letterSpacing: 0.5 }}
            >
              {name}
            </Text>
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  }

  // Grid mode (for 3-column layout)
  if (displayMode === "grid") {
    return (
      <Animated.View style={animatedStyle}>
        <TouchableOpacity
          className="items-center p-2"
          onPress={handlePress}
          activeOpacity={0.7}
        >
          <View style={{ 
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.3,
            shadowRadius: 5,
            elevation: 8,
            borderRadius: 12
          }}>
            {imageLoading ? (
              <SkeletonPlaceholder width={64} height={64} style={{ borderRadius: 12, marginBottom: 8 }} />
            ) : imageLoadError ? (
              <View style={[styles.errorIconContainer, { width: 64, height: 64, marginBottom: 8 }]}>
                <ImageOff size={32} color="#9ca3af" />
              </View>
            ) : (
              <Image
                source={getImageSource()} // getImageSource now returns an object { uri: string } or a number for local assets
                style={styles.logoImageGrid} // Use StyleSheet for image styles
                contentFit="contain"
                placeholder={require('../../../assets/images/partial-react-logo.png')} // Added a placeholder for expo-image
                transition={300}
                onLoadStart={() => {
                  setImageLoading(true);
                  setImageLoadError(false);
                }}
                onLoadEnd={() => setImageLoading(false)}
                onError={(e) => {
                  console.error(`[Expo Image Error - Grid for ${name}] Source: ${JSON.stringify(getImageSource())}, Error: ${e.error?.message || 'Unknown error'}`);
                  setImageLoading(false);
                  setImageLoadError(true);
                }}
              />
            )}
          </View>
          <View className="w-full items-center">
            <View className="flex-row items-center justify-center">
              {getCategoryIcon()}
              <Text
                className="text-white text-xs font-bold text-center"
                numberOfLines={2}
                style={{ fontFamily: 'System', letterSpacing: 0.5 }}
              >
                {name}
              </Text>
            </View>
            {isFavorite && (
              <Animated.View
                entering={FadeIn.springify().damping(10).stiffness(100)}
              >
                <Star size={12} color="#FFC107" fill="#FFC107" className="mt-1" />
              </Animated.View>
            )}
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  }

  // Vertical mode (expanded with details) - iPhone style
  return (
    <Animated.View style={animatedStyle}>
      <TouchableOpacity
        className="flex-row items-center p-4"
        onPress={handlePress}
        activeOpacity={0.7}
      >
        <View style={{ 
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.3,
          shadowRadius: 5,
          elevation: 8,
          borderRadius: 12
        }}>
          {imageLoading ? (
            <SkeletonPlaceholder width={48} height={48} style={{ borderRadius: 12 }} />
          ) : imageLoadError ? (
            <View style={[styles.errorIconContainer, { width: 48, height: 48 }]}>
              <ImageOff size={24} color="#9ca3af" />
            </View>
          ) : (
            <Image
              source={getImageSource()} // getImageSource now returns an object { uri: string } or a number for local assets
              style={styles.logoImageVertical} // Use StyleSheet for image styles
              contentFit="contain"
              placeholder={require('../../../assets/images/partial-react-logo.png')} // Added a placeholder for expo-image
              transition={300}
              onLoadStart={() => {
                setImageLoading(true);
                setImageLoadError(false);
              }}
              onLoadEnd={() => setImageLoading(false)}
              onError={(e) => {
                console.error(`[Expo Image Error - Vertical for ${name}] Source: ${JSON.stringify(getImageSource())}, Error: ${e.error?.message || 'Unknown error'}`);
                setImageLoading(false);
                setImageLoadError(true);
              }}
            />
          )}
        </View>
        <View className="flex-1 ml-3">
          <View className="flex-row justify-between items-center">
            <View className="flex-row items-center">
              <View className="mr-1">
                {getCategoryIcon()}
              </View>
              <Text 
                className="text-white text-base font-bold"
                style={{ fontFamily: 'System', letterSpacing: 0.5 }}
              >
                {name}
              </Text>
            </View>
            {isFavorite && (
              <Animated.View
                entering={FadeIn.springify().damping(10).stiffness(100)}
              >
                <Star size={16} color="#FFC107" fill="#FFC107" />
              </Animated.View>
            )}
          </View>
          {description && (
            <Text className="text-gray-400 text-xs mt-1" numberOfLines={1}>
              {description}
            </Text>
          )}
        </View>
        <ExternalLink size={16} color="#A0AEC0" className="ml-2" />
      </TouchableOpacity>
    </Animated.View>
  );
};

interface SkeletonPlaceholderProps {
  width: number;
  height: number;
  style?: object; // Allow additional styles
}

// Simple Skeleton Placeholder Component
const SkeletonPlaceholder: React.FC<SkeletonPlaceholderProps> = ({ width, height, style }) => (
  <View style={[
    styles.skeleton,
    { width, height },
    style
  ]} />
);

const styles = StyleSheet.create({
  logoImageHorizontal: {
    width: 64,
    height: 64,
    borderRadius: 12,
  },
  logoImageGrid: {
    width: 64,
    height: 64,
    borderRadius: 12,
    marginBottom: 8,
  },
  logoImageVertical: {
    width: 48, // Corrected from 80 to 48 to match className="w-12 h-12"
    height: 48,
    borderRadius: 12, // Corrected from 16 to 12 to match rounded-xl
  },
  skeleton: {
    backgroundColor: '#374151', // bg-gray-700
    borderRadius: 12, // Corresponds to rounded-xl
  },
  errorIconContainer: {
    width: '100%',
    height: '100%',
    backgroundColor: '#374151', // bg-gray-700
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  }
});

export default memo(WebsiteCard);
