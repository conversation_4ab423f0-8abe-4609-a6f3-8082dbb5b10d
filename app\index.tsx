import React, { useState, useCallback } from "react";
import {
  View,
  Text,
  SafeAreaView,
  TouchableOpacity,
  StatusBar,
  Platform,
} from "react-native";
import { BookmarkIcon, Settings } from "lucide-react-native";
import WebsiteList from "./components/WebsiteList";
import WebViewScreen from "./components/WebViewScreen";
import TabNavigation, { Tab } from "./components/TabNavigation";
import ErrorBoundary from "./components/ErrorBoundary";

// Cross-platform shadow utility
const createShadowStyle = (elevation: number = 4, opacity: number = 0.3, radius: number = 3) => {
  if (Platform.OS === 'web') {
    return {
      boxShadow: `0px 2px ${radius}px rgba(0, 0, 0, ${opacity})`,
    };
  }
  return {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: opacity,
    shadowRadius: radius,
    elevation,
  };
};

const HomeScreen = React.memo(function HomeScreen() {
  const [showFavorites, setShowFavorites] = useState(false);
  const [tabs, setTabs] = useState<Tab[]>([]);
  const [activeTabId, setActiveTabId] = useState<string | null>(null);

  // Generate a unique ID for tabs
  const generateTabId = () =>
    `tab-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  // Handle website selection - create a new tab
  const handleWebsitePress = useCallback((website) => {
    // Check if this website is already open in a tab
    const existingTab = tabs.find((tab) => tab.url === website.url);

    if (existingTab) {
      // If the website is already open, just switch to that tab
      setActiveTabId(existingTab.id);
    } else {
      // Otherwise create a new tab
      const newTabId = generateTabId();
      const newTab = {
        id: newTabId,
        title: website.name,
        url: website.url,
        favicon: website.logoUrl,
      };

      setTabs((prevTabs) => [...prevTabs, newTab]);
      setActiveTabId(newTabId);
    }
  }, [tabs, activeTabId]);

  // Handle tab press - switch to that tab
  const handleTabPress = useCallback((tabId: string) => {
    setActiveTabId(tabId);
  }, []);

  // Handle tab close
  const handleTabClose = useCallback((tabId: string) => {
    setTabs((prevTabs) => prevTabs.filter((tab) => tab.id !== tabId));

    // If we closed the active tab, switch to the last tab or null if no tabs left
    if (activeTabId === tabId) {
      setActiveTabId((prevActiveTabId) => {
        const remainingTabs = tabs.filter((tab) => tab.id !== tabId); // Re-filter to ensure correct state
        return remainingTabs.length > 0
          ? remainingTabs[remainingTabs.length - 1].id
          : null;
      });
    }
  }, [tabs, activeTabId]);

  // Handle new tab creation (from the + button)
  const handleNewTab = useCallback(() => {
    // Go back to home screen without closing any tabs
    // This preserves all existing tabs
    setActiveTabId(null);
  }, []);

  // Toggle favorites view
  const toggleFavoritesView = useCallback(() => {
    setShowFavorites((prevShowFavorites) => !prevShowFavorites);
  }, []);

  // Handle tab close callback for WebViewScreen
  const handleWebViewClose = useCallback(() => {
    if (activeTabId) {
      handleTabClose(activeTabId);
    }
  }, [activeTabId, handleTabClose]);

  // Handle title change callback for WebViewScreen
  const handleTitleChange = useCallback((newTitle: string) => {
    if (activeTabId) {
      setTabs((prevTabs) =>
        prevTabs.map((tab) =>
          tab.id === activeTabId ? { ...tab, title: newTitle } : tab,
        ),
      );
    }
  }, [activeTabId]);

  // Handle favicon change callback for WebViewScreen
  const handleFaviconChange = useCallback((faviconUrl: string) => {
    if (activeTabId) {
      setTabs((prevTabs) =>
        prevTabs.map((tab) =>
          tab.id === activeTabId
            ? { ...tab, favicon: faviconUrl }
            : tab,
        ),
      );
    }
  }, [activeTabId]);

  // Get the current active tab
  const activeTab = tabs.find((tab) => tab.id === activeTabId);

  return (
    <ErrorBoundary>
      <SafeAreaView className="flex-1 bg-[#0c1220]">
        <StatusBar barStyle="light-content" backgroundColor="#111827" />

        {activeTab ? (
          <View className="flex-1">
            <WebViewScreen
              key={activeTabId} // key is already here, good.
              url={activeTab.url}
              title={activeTab.title}
              onClose={handleWebViewClose}
              onTitleChange={handleTitleChange}
              onFaviconChange={handleFaviconChange}
            />
            <TabNavigation
              tabs={tabs}
              activeTabId={activeTabId}
              onTabPress={handleTabPress}
              onTabClose={handleTabClose}
              onNewTab={handleNewTab}
            />
          </View>
        ) : (
          <View className="flex-1">
            {/* Header - iPhone style */}
            <View className="flex-row justify-between items-center px-4 py-3 bg-gray-950">
              <Text className="text-white text-2xl font-bold">Media Hub</Text>
              <TouchableOpacity
                className="p-2 rounded-full bg-gray-800"
                onPress={() => {
                  // Navigate to settings page
                  // In a real app, this would use router.push('/settings')
                  alert('Settings page would open here');
                }}
              >
                <Settings size={20} color="white" />
              </TouchableOpacity>
            </View>

            {/* Main Content */}
            <WebsiteList
              showFavoritesOnly={showFavorites}
              onWebsitePress={handleWebsitePress}
              onToggleFavorite={(websiteId) => {
                // This would typically update the favorite status in a real app
                console.log(`Toggle favorite for website ${websiteId}`);
              }}
            />

            {/* Favorites Toggle Button */}
            <TouchableOpacity
              onPress={toggleFavoritesView}
              className={`absolute bottom-6 right-6 p-3 rounded-full ${showFavorites ? "bg-amber-600" : "bg-gray-800"}`}
              style={{
                ...createShadowStyle(4, 0.3, 3),
                zIndex: 5,
              }}
            >
              <BookmarkIcon
                size={24}
                color={showFavorites ? "#ffffff" : "#d1d5db"}
              />
            </TouchableOpacity>

            {/* Show tab bar even on home screen if there are tabs */}
            {tabs.length > 0 && (
              <TabNavigation
                tabs={tabs}
                activeTabId={activeTabId}
                onTabPress={handleTabPress}
                onTabClose={handleTabClose}
                onNewTab={handleNewTab}
              />
            )}
          </View>
        )}
      </SafeAreaView>
    </ErrorBoundary>
  );
});

export default HomeScreen;
